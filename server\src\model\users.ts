import mongoose from "mongoose";
import { SchemaType } from "mongoose";

const userShema = new mongoose.Schema({
    username: {
        type: String,
        required: true,
        unique: true,
    },
    password: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Password"
    },
    email: {
        type: String,
        required: true,
        unique: true,
    },
    mobile:{
        type: mongoose.Schema.Types.ObjectId,
        ref: "Mobile"
    },
    hasPassword:{
        type: Boolean,
        required: true,
        default: false,
    },
    hasEmail:{
        type: Boolean,
        required: true,
        default: false,        
    },
    hasMobile:{
        type: Boolean,
        required: true,
        default: false,        
    },
    isUser:{
        type: Boolean,
        required: true,
        default: true,
    },
    isAdmin:{
        type: Boolean,
        required: true,
        default: false,
    },
    isShop:{
        type: Boolean,
        required: true,
        default: false,        
    },
})

const User = mongoose.model("User", userShema);

export default User;
import 'package:background_fetch/background_fetch.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:golbarg/app.dart';
import 'package:golbarg/extensions/Auth.dart';
import 'package:golbarg/extensions/autorouter.dart';
import 'package:golbarg/extensions/theme.dart';
import 'package:golbarg/services/background.dart';
import 'package:golbarg/services/notification.dart';
import 'package:persian_datetime_picker/persian_datetime_picker.dart';
import 'package:url_strategy/url_strategy.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await dotenv.load(fileName: ".env.development");
  setPathUrlStrategy();
  final container = ProviderContainer();
  // final notificationService = container.read(notificationServiceProvider);
  // await notificationService.init();
  // BackgroundFetch.registerHeadlessTask(backgroundFetchHeadlessTask);
  // await BackgroundFetch.start();
  
  runApp(const ProviderScope(child: Gandomak()));
}
class Gandomak extends ConsumerStatefulWidget {
  @override
  ConsumerState<Gandomak> createState() => _GandomakState();
  const Gandomak({super.key});
}

class _GandomakState extends ConsumerState<Gandomak> {

  @override
  void initState() {
    super.initState();
    // final mqttService = ref.read(mqttProvider);
    // mqttService.connect();

  }

  @override
  Widget build(BuildContext context) {
    final appRouter = AppRouter(authState: ref.watch(authProvider),);
    return AuthWrapper(
      child: MaterialApp.router(
        title: 'گندمک',
        routerConfig: appRouter.config(),
        debugShowCheckedModeBanner: false,
        themeMode: ref.watch(themeProvider),
        locale: const Locale("fa", "IR"),
        supportedLocales: const [
          Locale("fa", "IR"),
          Locale("en", "US"),
        ],
        localizationsDelegates: const [
          // Add Localization
          PersianMaterialLocalizations.delegate,
          PersianCupertinoLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        theme: AppTheme.light,
        darkTheme: AppTheme.dark,
      ),
    );
  }
}

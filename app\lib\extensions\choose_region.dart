import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Export the extension

var regionProvider = StateProvider<String>((ref) {
  return 'ir';
});

const List regions = [
  'ir',
  'us',
  'uk',
  'au',
  'ca',
  'de',
  'fr',
  'it',
  'jp',
  'kr',
  'mx',
  'nl',
  'no',
  'pl',
  'ru',
  'se',
  'tr',
  'za',
];

class RegionSelector extends ConsumerWidget {
  const RegionSelector({super.key});
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return DropdownButton<String>(
      value: ref.watch(regionProvider),
      onChanged: (String? newValue) {
        ref.read(regionProvider.notifier).state = newValue!;
      },
      items: regions.map<DropdownMenuItem<String>>((String value) {
        return DropdownMenuItem<String>(
          value: value,
          child: Text(value),
        );
      }).toList(),
    );
  }
}

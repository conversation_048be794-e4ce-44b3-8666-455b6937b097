import 'package:auto_route/auto_route.dart';
import 'package:golbarg/extensions/Auth.dart';
import 'package:golbarg/extensions/autorouter.gr.dart';

// Removed the part-of directive as it should be in the part file.

@AutoRouterConfig(replaceInRouteName: 'Screen|Page,Route')
class AppRouter extends RootStackRouter {
  final AuthState authState;
  AppRouter({required this.authState});
  @override
  List<AutoRoute> get routes => [
        AutoRoute(
          page: Gol.page,
          initial: true,
          children: [
            AutoRoute(page: HomeRoute.page, path: 'home'),
            AutoRoute(page: NotFound.page, path: 'profile'),
          ],
        ),
        AutoRoute(page: LoginRoute.page, path: '/login'),
        AutoRoute(page: WelcomeRoute.page, path: '/welcome'),
        AutoRoute(page: FAQRoute.page, path: '/faq'),
        AutoRoute(page: PrivacyRoute.page, path: '/privacy'),
        AutoRoute(page: NotFound.page, path: '/:path.*'),
      ];

  @override
  List<AutoRouteGuard> get guards => [
//        AuthGuard(),        
      ];
}



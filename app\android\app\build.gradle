plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

android {
    namespace = "ir.golbarg.app"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        applicationId = "ir.golbarg.app"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

                dependencies {
                    // For AGP 7.4+
                    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.4'
                    // For AGP 7.3
                    // coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.2.3'
                    // For AGP 4.0 to 7.2
                    // coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.1.9'
                }
    buildTypes {
        release {
            signingConfig = signingConfigs.debug
            shrinkResources true
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}
allprojects {
    repositories {
                maven { url "${project(':background_fetch').projectDir}/libs" }
    }
}
dependencies {
    implementation "androidx.credentials:credentials:1.5.0"
    implementation "androidx.credentials:credentials-play-services-auth:1.5.0"
    implementation "com.google.android.libraries.identity.googleid:googleid:1.1.1"
}
flutter {
    source = "../.."
}

import mongoose, { Mongoose } from "mongoose";
import { SchemaType } from "mongoose";

const errorSchema = new mongoose.Schema({
  message: {
    type: String,
    required: true,
  },
  code: {
    type: Number,
    required: true,
  },
  status: {
    type: String,
    required: true,
  },
  stack: {
    type: String,
    required: false,
  },
  timestamp: {
    type: Date,
    required: true,
    default: Date.now,
  },
  request: {
    type: String,
    required: true,
  },
  user : {
    type: mongoose.SchemaTypes.ObjectId,
    ref: "User",
    required: false,
  },
});

const Error = mongoose.model("Error", errorSchema);

export default Error;

import mongoose from "mongoose";
import { ref } from "process";

const notificationSchema = new mongoose.Schema({
    deviceId: String,
    user:{
        type: mongoose.Types.ObjectId,
        ref: "User"
    },
    NotifType:{
        type: String,
        enum: ["Push", "In-app", "Both"],
        default: "Both",
    },
    hasBeenRead:{
        type: Boolean,
        default: false
    },
    
})

const Notification = mongoose.model("Notification", notificationSchema)

export default Notification;
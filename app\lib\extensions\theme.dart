import 'package:flex_color_scheme/flex_color_scheme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

abstract final class AppTheme {
  // The FlexColorScheme defined light mode ThemeData.
  static ThemeData light = FlexThemeData.light(
    // Using FlexColorScheme built-in FlexScheme enum based colors
    scheme: FlexScheme.materialHc,
    // Input color modifiers.
    swapLegacyOnMaterial3: true,
    // Surface color adjustments.
    lightIsWhite: true,
    // Convenience direct styling properties.
    bottomAppBarElevation: 0.5,
    // Component theme configurations for light mode.
    subThemesData: const FlexSubThemesData(
      interactionEffects: true,
      blendOnLevel: 10,
      useM2StyleDividerInM3: true,
      splashType: FlexSplashType.instantSplash,
      splashTypeAdaptive: FlexSplashType.instantSplash,
      adaptiveElevationShadowsBack: FlexAdaptive.all(),
      adaptiveAppBarScrollUnderOff: FlexAdaptive.all(),
      defaultRadius: 6.0,
      elevatedButtonSchemeColor: SchemeColor.onPrimaryContainer,
      elevatedButtonSecondarySchemeColor: SchemeColor.primaryContainer,
      outlinedButtonSchemeColor: SchemeColor.onSurface,
      outlinedButtonOutlineSchemeColor: SchemeColor.outlineVariant,
      toggleButtonsBorderSchemeColor: SchemeColor.outlineVariant,
      segmentedButtonSchemeColor: SchemeColor.primary,
      segmentedButtonBorderSchemeColor: SchemeColor.outlineVariant,
      switchThumbSchemeColor: SchemeColor.onPrimaryContainer,
      switchAdaptiveCupertinoLike: FlexAdaptive.all(),
      unselectedToggleIsColored: true,
      sliderValueTinted: true,
      sliderTrackHeight: 8,
      inputDecoratorIsDense: true,
      inputDecoratorContentPadding: EdgeInsetsDirectional.fromSTEB(12, 12, 12, 12),
      inputDecoratorBorderSchemeColor: SchemeColor.primary,
      inputDecoratorBorderType: FlexInputBorderType.outline,
      inputDecoratorRadius: 8.0,
      inputDecoratorBorderWidth: 0.5,
      inputDecoratorFocusedBorderWidth: 2.0,
      fabUseShape: true,
      fabAlwaysCircular: true,
      chipSchemeColor: SchemeColor.secondaryContainer,
      chipSelectedSchemeColor: SchemeColor.primaryContainer,
      chipFontSize: 12,
      chipIconSize: 16,
      chipPadding: EdgeInsetsDirectional.fromSTEB(4, 4, 4, 4),
      cardRadius: 12.0,
      popupMenuRadius: 6.0,
      popupMenuElevation: 4.0,
      alignedDropdown: true,
      tooltipRadius: 6,
      tooltipSchemeColor: SchemeColor.surfaceContainerHigh,
      tooltipOpacity: 0.96,
      dialogBackgroundSchemeColor: SchemeColor.surfaceContainerHigh,
      dialogRadius: 12.0,
      snackBarRadius: 6,
      snackBarElevation: 6,
      snackBarBackgroundSchemeColor: SchemeColor.surfaceContainerLow,
      appBarBackgroundSchemeColor: SchemeColor.surfaceContainerLowest,
      appBarScrolledUnderElevation: 0.5,
      bottomAppBarHeight: 60,
      tabBarIndicatorWeight: 4,
      tabBarIndicatorTopRadius: 4,
      tabBarDividerColor: Color(0x00000000),
      drawerRadius: 0.0,
      drawerElevation: 2.0,
      drawerIndicatorOpacity: 0.5,
      bottomSheetBackgroundColor: SchemeColor.surfaceContainerHigh,
      bottomSheetModalBackgroundColor: SchemeColor.surfaceContainer,
      bottomSheetRadius: 12.0,
      bottomSheetElevation: 4.0,
      bottomSheetModalElevation: 6.0,
      bottomNavigationBarSelectedLabelSchemeColor: SchemeColor.primary,
      bottomNavigationBarMutedUnselectedLabel: true,
      bottomNavigationBarSelectedIconSchemeColor: SchemeColor.primary,
      bottomNavigationBarMutedUnselectedIcon: true,
      bottomNavigationBarBackgroundSchemeColor: SchemeColor.surfaceContainer,
      bottomNavigationBarElevation: 0.0,
      menuRadius: 6.0,
      menuElevation: 4.0,
      menuSchemeColor: SchemeColor.surfaceContainerLowest,
      menuPadding: EdgeInsetsDirectional.fromSTEB(6, 10, 5, 10),
      menuBarRadius: 0.0,
      menuBarElevation: 0.0,
      menuBarShadowColor: Color(0x00000000),
      menuIndicatorBackgroundSchemeColor: SchemeColor.surfaceContainerHigh,
      menuIndicatorRadius: 6.0,
      searchBarElevation: 0.0,
      searchViewElevation: 0.0,
      navigationBarIndicatorSchemeColor: SchemeColor.secondaryContainer,
      navigationBarBackgroundSchemeColor: SchemeColor.surfaceContainer,
      navigationBarElevation: 0.0,
      navigationBarHeight: 72.0,
      navigationRailUseIndicator: true,
      navigationRailIndicatorSchemeColor: SchemeColor.secondaryContainer,
      navigationRailIndicatorOpacity: 1.00,
      navigationRailBackgroundSchemeColor: SchemeColor.surfaceContainer,
    ),
    // ColorScheme seed generation configuration for light mode.
    keyColors: const FlexKeyColors(
      useSecondary: true,
      useTertiary: true,
      useError: true,
      keepPrimary: true,
      keepSecondary: true,
      keepError: true,
      keepTertiaryContainer: true,
    ),
    tones: FlexSchemeVariant.chroma
        .tones(Brightness.light)
        .higherContrastFixed()
        .monochromeSurfaces(),
    // Direct ThemeData properties.
    visualDensity: FlexColorScheme.comfortablePlatformDensity,
    materialTapTargetSize: MaterialTapTargetSize.padded,
    cupertinoOverrideTheme: const CupertinoThemeData(applyThemeToAll: true),
    fontFamily: "Dana"
  );

  // The FlexColorScheme defined dark mode ThemeData.
  static ThemeData dark = FlexThemeData.dark(
    // Using FlexColorScheme built-in FlexScheme enum based colors.
    scheme: FlexScheme.materialHc,
    // Input color modifiers.
    swapLegacyOnMaterial3: true,
    // Convenience direct styling properties.
    bottomAppBarElevation: 0.5,
    fontFamily: "Dana",
    // Component theme configurations for dark mode.
    subThemesData: const FlexSubThemesData(
      interactionEffects: true,
      blendOnLevel: 20,
      blendOnColors: true,
      useM2StyleDividerInM3: true,
      splashType: FlexSplashType.instantSplash,
      splashTypeAdaptive: FlexSplashType.instantSplash,
      adaptiveElevationShadowsBack: FlexAdaptive.all(),
      adaptiveAppBarScrollUnderOff: FlexAdaptive.all(),
      defaultRadius: 6.0,
      elevatedButtonSchemeColor: SchemeColor.onPrimaryContainer,
      elevatedButtonSecondarySchemeColor: SchemeColor.primaryContainer,
      outlinedButtonSchemeColor: SchemeColor.onSurface,
      outlinedButtonOutlineSchemeColor: SchemeColor.outlineVariant,
      toggleButtonsBorderSchemeColor: SchemeColor.outlineVariant,
      segmentedButtonSchemeColor: SchemeColor.primary,
      segmentedButtonBorderSchemeColor: SchemeColor.outlineVariant,
      switchThumbSchemeColor: SchemeColor.onPrimaryContainer,
      switchAdaptiveCupertinoLike: FlexAdaptive.all(),
      unselectedToggleIsColored: true,
      sliderValueTinted: true,
      sliderTrackHeight: 8,
      inputDecoratorIsDense: true,
      inputDecoratorContentPadding: EdgeInsetsDirectional.fromSTEB(12, 12, 12, 12),
      inputDecoratorBorderSchemeColor: SchemeColor.primary,
      inputDecoratorBorderType: FlexInputBorderType.outline,
      inputDecoratorRadius: 8.0,
      inputDecoratorBorderWidth: 0.5,
      inputDecoratorFocusedBorderWidth: 2.0,
      fabUseShape: true,
      fabAlwaysCircular: true,
      chipSchemeColor: SchemeColor.secondaryContainer,
      chipSelectedSchemeColor: SchemeColor.primaryContainer,
      chipFontSize: 12,
      chipIconSize: 16,
      chipPadding: EdgeInsetsDirectional.fromSTEB(4, 4, 4, 4),
      cardRadius: 12.0,
      popupMenuRadius: 6.0,
      popupMenuElevation: 4.0,
      alignedDropdown: true,
      tooltipRadius: 6,
      tooltipSchemeColor: SchemeColor.surfaceContainerHigh,
      tooltipOpacity: 0.96,
      dialogBackgroundSchemeColor: SchemeColor.surfaceContainerHigh,
      dialogRadius: 12.0,
      snackBarRadius: 6,
      snackBarElevation: 6,
      snackBarBackgroundSchemeColor: SchemeColor.surfaceContainerLow,
      appBarBackgroundSchemeColor: SchemeColor.surfaceContainerLowest,
      appBarScrolledUnderElevation: 2.5,
      bottomAppBarHeight: 60,
      tabBarIndicatorWeight: 4,
      tabBarIndicatorTopRadius: 4,
      tabBarDividerColor: Color(0x00000000),
      drawerRadius: 0.0,
      drawerElevation: 2.0,
      drawerIndicatorOpacity: 0.5,
      bottomSheetBackgroundColor: SchemeColor.surfaceContainerHigh,
      bottomSheetModalBackgroundColor: SchemeColor.surfaceContainer,
      bottomSheetRadius: 12.0,
      bottomSheetElevation: 4.0,
      bottomSheetModalElevation: 6.0,
      bottomNavigationBarSelectedLabelSchemeColor: SchemeColor.primary,
      bottomNavigationBarMutedUnselectedLabel: true,
      bottomNavigationBarSelectedIconSchemeColor: SchemeColor.primary,
      bottomNavigationBarMutedUnselectedIcon: true,
      bottomNavigationBarBackgroundSchemeColor: SchemeColor.surfaceContainer,
      bottomNavigationBarElevation: 0.0,
      menuRadius: 6.0,
      menuElevation: 4.0,
      menuSchemeColor: SchemeColor.surfaceContainerLowest,
      menuPadding: EdgeInsetsDirectional.fromSTEB(6, 10, 5, 10),
      menuBarRadius: 0.0,
      menuBarElevation: 0.0,
      menuBarShadowColor: Color(0x00000000),
      menuIndicatorBackgroundSchemeColor: SchemeColor.surfaceContainerHigh,
      menuIndicatorRadius: 6.0,
      searchBarElevation: 0.0,
      searchViewElevation: 0.0,
      navigationBarIndicatorSchemeColor: SchemeColor.secondaryContainer,
      navigationBarBackgroundSchemeColor: SchemeColor.surfaceContainer,
      navigationBarElevation: 0.0,
      navigationBarHeight: 72.0,
      navigationRailUseIndicator: true,
      navigationRailIndicatorSchemeColor: SchemeColor.secondaryContainer,
      navigationRailIndicatorOpacity: 1.00,
      navigationRailBackgroundSchemeColor: SchemeColor.surfaceContainer,
    ),
    // ColorScheme seed configuration setup for dark mode.
    keyColors: const FlexKeyColors(
      useSecondary: true,
      useTertiary: true,
      useError: true,
      keepPrimary: true,
      keepSecondary: true,
      keepError: true,
      keepTertiaryContainer: true,
    ),
    tones: FlexSchemeVariant.chroma
        .tones(Brightness.dark)
        .higherContrastFixed()
        .monochromeSurfaces(),
    // Direct ThemeData properties.
    visualDensity: FlexColorScheme.comfortablePlatformDensity,
    materialTapTargetSize: MaterialTapTargetSize.padded,
    cupertinoOverrideTheme: const CupertinoThemeData(applyThemeToAll: true),
  );
}


final themeProvider = StateNotifierProvider<ThemeNotifier, ThemeMode>((ref) {
  return ThemeNotifier();
});

class ThemeNotifier extends StateNotifier<ThemeMode> {
  ThemeNotifier() : super(ThemeMode.system) {
    _loadTheme();
  }

  void _loadTheme() async {
    final prefs = await SharedPreferences.getInstance();
    final savedTheme = prefs.getString('theme');
    if (savedTheme != null) {
      state = ThemeMode.values.firstWhere((e) => e.toString() == savedTheme);
    }
  }

  void toggleTheme() {
    final newTheme = state == ThemeMode.light ? ThemeMode.dark : ThemeMode.light;
    setTheme(newTheme);
  }

  void setTheme(ThemeMode mode) async {
    state = mode;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('theme', mode.toString());
  }
}

import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:golbarg/extensions/api.dart';
import 'package:golbarg/extensions/client.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:uuid/uuid.dart';

final refreshUser = FutureProvider((ref) async {
  try {
    final client = await ref.getDebouncedHttpClient();
    final auth = ref.read(authProvider);
    final apiUrl = ApiExtension.apiUrl;
    final response = await client.get(Uri.parse('$apiUrl/refreshToken'),
        headers: {'Authorization': 'Bearer ${auth.token}'});
    final token = jsonDecode(response.body)['token'];
    ref.watch(authProvider.notifier).login(token);
  } catch (e) {
    throw Exception('Failed to refresh user');
  }
});

class AuthState {
  final bool isAuthenticated;
  final String token;
  final Map<String, dynamic> user;
  final String uuid;
  final bool isFarmer;
  final bool isGuest;
  final bool isShop;

  AuthState(
      {required this.isAuthenticated,
      required this.token,
      required this.user,
      required this.uuid,
      required this.isLoading,
      required this.error,
      required this.isGuest,
      required this.isFarmer, required this.isShop});

  static const String dummyJwt =
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyIjp7ImlkIjoiMCIsIm5hbWUiOiJHdWVzdCIsInJvbGUiOiJub24tdXNlciJ9fQ.sD3Jp1z5X8y7z8x9y6z5x4y3w2v1";

  bool isLoading;

  var error;
}

class AuthNotifier extends StateNotifier<AuthState> {
  AuthNotifier()
      : super(AuthState(
          isAuthenticated: false,
          token: "",
          user: {},
          uuid: "",
          isLoading: true,
          error: null,
          isFarmer: false,
          isGuest: true,
          isShop: false
        )) {
    _init();
  }

  Future<void> _init() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('token');
    String? uuid = prefs.getString('uuid');

    // Generate and save UUID if it doesn't exist
    if (uuid == null) {
      uuid = const Uuid().v4();
      await prefs.setString("uuid", uuid);
    }

    // Initialize state with UUID regardless of authentication
    if (token != null && !JwtDecoder.isExpired(token)) {
      final decoded = JwtDecoder.decode(token);
      state = AuthState(
          isAuthenticated: true,
          token: token,
          user: decoded["user"],
          isLoading: false,
          error: null,
          isFarmer: decoded["user"]["isFarmer"],
          isGuest: decoded["user"]["isGuest"],
          isShop: decoded["user"]["isShop"],
          uuid: uuid);
    } else {
      state = AuthState(
        isAuthenticated: false,
        token: "",
        user: {},
        uuid: uuid,
        isLoading: false,
        error: null,
        isGuest: true,
        isFarmer: false,
        isShop: false
      );
    }
  }

  Future<void> login(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('token', token);
    final decoded = JwtDecoder.decode(token);
    // Get existing UUID
    final uuid = prefs.getString('uuid') ?? const Uuid().v4();
    state = AuthState(
        isLoading: false,
        error: null,
        isAuthenticated: true,
        token: token,
        user: decoded["user"],
        isFarmer: decoded["user"]["isFarmer"],
        isGuest: decoded["user"]["isGuest"],
        isShop: decoded["user"]["isShop"],
        uuid: uuid);
  }

  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('token');
    // Keep the UUID but clear other auth data
    final uuid = prefs.getString('uuid') ?? '';
    state = AuthState(
      isAuthenticated: false,
      token: '',
      user: {},
      uuid: uuid,
      isLoading: false,
      error: null,
      isGuest: true,
      isFarmer: false,
      isShop: false
    );
  }

  Future<void> refreshToken() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('token');
    final uuid = prefs.getString('uuid') ?? '';

    if (token != null && !JwtDecoder.isExpired(token)) {
      final decoded = JwtDecoder.decode(token);
      state = AuthState(
          isAuthenticated: true,
          token: token,
          user: decoded["user"],
          isLoading: false,
          error: null,
          isFarmer: decoded["user"]["isFarmer"],
        isGuest: decoded["user"]["isGuest"],
        isShop: decoded["user"]["isShop"],
          uuid: uuid);
    } else {
      state = AuthState(
        isAuthenticated: false,
        token: '',
        user: {},
        uuid: uuid,
        isLoading: false,
        error: null,
        isFarmer: false,
        isGuest: true,
        isShop: false,        
      );
    }
  }
}

final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier();
});


class AuthWrapper extends ConsumerStatefulWidget {
  final Widget child;

  const AuthWrapper({required this.child, Key? key}) : super(key: key);

  @override
  ConsumerState<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends ConsumerState<AuthWrapper> {
  Timer? _refreshTimer;

  @override
  void initState() {
    super.initState();
    _setupTokenRefresh();
    ref.read(authProvider.notifier).refreshToken();
  }

  void _setupTokenRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(
      const Duration(minutes: 15),
      (_) => ref.read(authProvider.notifier).refreshToken(),
    );
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

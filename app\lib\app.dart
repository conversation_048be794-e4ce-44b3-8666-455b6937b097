import 'package:auto_route/annotations.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:golbarg/extensions/autorouter.gr.dart';
import 'package:golbarg/services/googleSignIn.dart';

@RoutePage()
class Gol extends ConsumerWidget {
  const Gol({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return const GuestRouter();
  }
}

class GuestRouter extends ConsumerWidget {
  const GuestRouter({super.key});
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AutoTabsRouter(
      curve: Curves.bounceIn,
      routes: [
        HomeRoute(),
        NotFound()
      ],
      transitionBuilder: (context, child, animation) => FadeTransition(
        opacity: animation,
        child: child,
      ),
      builder: (context, child) {
        final tabsRouter = AutoTabsRouter.of(context);
        return Scaffold(
          body: child,
          appBar: AppBar(
            elevation: 5,
            leading: GestureDetector(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Image.asset("./images/logo.png"),
              ),
              onTap: () {
                AutoRouter.of(context).push(const WelcomeRoute());
              },
            ),
            leadingWidth: 48,
          ),
          bottomNavigationBar: BottomNavigationBar(
            currentIndex: tabsRouter.activeIndex,
            onTap: tabsRouter.setActiveIndex,
            items: const [
               BottomNavigationBarItem(
                icon: Icon(Icons.home),
                label: 'خانه',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.person),
                label: 'پروفایل',
              ),
            ],
          ),
        );
      },
    );
  }
}

import express, { Request, Response } from 'express';
import User from "../../model/users";
import Error from "../../model/errors";
import {sign,verify} from "jsonwebtoken";
import { compare } from 'bcrypt';



/// Sign jwt 
const signToken = (id: string) => {
   return sign({ id }, process.env.JWT_SECRET!, {
      expiresIn: process.env.JWT_EXPIRES_IN ? Number(process.env.JWT_EXPIRES_IN) : '7d',
    });
  };

  const createSendToken = (user: any, statusCode: number, res: Response) => {
    const token = signToken(user._id);
    const cookieOptions = {
      expires: new Date(
        Date.now() + (Number(process.env.JWT_COOKIE_EXPIRES_IN) || 7) * 24 * 60 * 60 * 1000
      ),
      httpOnly: true,
    };
    res.cookie('jwt', token, cookieOptions);
    res.status(statusCode).json({
      status: 'success',
      token,
      data: {
        user,
      },
    });
  };

  const sendSuccess = (res: Response, statusCode: number, data: any) => {
    res.status(statusCode).json({
      status: 'success',
      data,
    });
  };
  const sendError = (res: Response, statusCode: number, message: string, req: Request, userId: string) => {
    const error = new Error({
      message,
      code: statusCode,
      status: 'error',
      request: req.originalUrl,
      user: userId || null,
    });
    error.save();
    res.status(statusCode).json({
      status: 'error',
      message,
    });
  };
  

  const auth = express.Router();

  auth.post('/login', async (req: Request, res: Response) => {
    try {
      const { username, password } = req.body;
      if (!username || !password) {
        return sendError(res, 400, 'Please provide username and password', req, "null");
      }
      const user = await User.findOne({ username });
      if (!user) {
        return sendError(res, 401, 'Incorrect username or password', req, "null");
      }
      const isPasswordCorrect = await compare(password, user.password);
      if (!isPasswordCorrect) {
        return sendError(res, 401, 'Incorrect username or password', req, user._id.toString());
      }
      createSendToken(user, 200, res);
    } catch (error) {
      sendError(res, 500, 'Something went wrong', req, "null");
    }
  });

  auth.post('/register', async (req: Request, res: Response) => {
    try {
      const { username, password, email, mobile } = req.body;
      if (!username || !password) {
        return sendError(res, 400, 'Please provide username and password', req, "null");
      }
      const user = await User.findOne({ username });
      if (user) {
        return sendError(res, 400, 'User already exists', req, user._id.toString());
      }
      const newUser = await User.create({
        username,
        password,
        email,
        mobile,
      });
      createSendToken(newUser, 201, res);
    } catch (error) {
      sendError(res, 500, 'Something went wrong', req, "null");
    }
  });

  auth.post('/logout', (req: Request, res: Response) => {
    res.cookie('jwt', 'loggedout', {
      expires: new Date(Date.now() + 10 * 1000),
      httpOnly: true,
    });
    res.status(200).json({
        status: 'success',
      });
  });

 /*  auth.post('/refreshToken', async (req: Request, res: Response) => {
    try {
      const token = req.headers.authorization?.split(' ')[1];
      if (!token) {
        return sendError(res, 401, 'You are not logged in! Please log in again.', req, null);
      }
      const decoded = verify(token, process.env.JWT_SECRET!);
      const user = await User.findById(decoded._id);
      if (!user) {
        return sendError(res, 401, 'The user belonging to this token does no longer exist.', req, null);
      }
      createSendToken(user, 200, res);
    }
    catch (error) {
      sendError(res, 500, 'Something went wrong', req, null);
    }
  }); */

  export default auth;


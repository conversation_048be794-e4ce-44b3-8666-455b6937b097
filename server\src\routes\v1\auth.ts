import express, { Request, Response } from 'express';
import User from "../../model/users";
import Error from "../../model/errors";
import {sign,verify} from "jsonwebtoken";
import { compare } from 'bcrypt';



/// Sign jwt 
const signToken = (user: ) => {
   return sign({ user }, process.env.JWT_SECRET!, {
      expiresIn: process.env.JWT_EXPIRES_IN ? Number(process.env.JWT_EXPIRES_IN) : '7d',
    });
  };

  const sendSuccess = (res: Response, statusCode: number, data: any) => {
    res.status(statusCode).json({
      status: 'success',
      data,
    });
  };

  /// sends [Error] 
  const sendError = (res: Response, statusCode: number, message: string, req: Request, userId: string) => {
    const error = new Error({
      message,
      code: statusCode,
      status: 'error',
      request: req.originalUrl,
      user: userId || null,
    });
    error.save();
    res.status(statusCode).json({
      status: 'error',
      message,
    });
  };

  
  









  const auth = express.Router();




  export default auth;


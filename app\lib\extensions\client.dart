import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:golbarg/extensions/api.dart';
import 'package:http/http.dart' as http;
import 'dart:async';
import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;

extension DebounceAndCancelExtension on Ref {
  /// Creates an HTTP client with CORS headers and debouncing capability
  Future<http.Client> getDebouncedHttpClient(
      [Duration? duration, Duration? timeout]) async {
    var didDispose = false;
    onDispose(() => didDispose = true);

    await Future<void>.delayed(duration ?? const Duration(milliseconds: 500));

    if (didDispose) {
      throw Exception('Cancelled');
    }

    // Create a custom client with CORS headers
    final client = _CorsClient();
    onDispose(client.close);

    return Future.any([
      Future.value(client),
      Future<void>.delayed(timeout ?? const Duration(seconds: 3)).then((_) {
        throw TimeoutException('Operation timed out');
      }),
    ]);
  }
}

/// Custom HTTP client that adds CORS headers to all requests
class _CorsClient extends http.BaseClient {
  final http.Client _inner = http.Client();

  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) {
    // Add CORS headers to all requests
    request.headers.addAll({
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers':
          'Origin, Content-Type, Accept, Authorization, X-Requested-With',
      'Access-Control-Allow-Credentials': 'true',
    });

    return _inner.send(request);
  }

  @override
  void close() {
    _inner.close();
    super.close();
  }
}

/// DebounceHttpClient wraps an http.Client and adds debounce, default headers, and timeout.

/// Network-aware debounce HTTP client
class DebounceHttpResult<T> {
  final T data;
  final String? error;

  DebounceHttpResult({required this.data, this.error});

  bool get hasError => error != null;
}

class DebounceHttpClient {
  final Duration debounceDuration;
  final Duration timeoutDuration;
  final Map<String, String> defaultHeaders;
  final String apiUrl;
  final http.Client _client;

  Timer? _debounceTimer;
  late final StreamSubscription<List<ConnectivityResult>> _connectivitySubscription;
  bool _isConnected = true; // assume connected initially

  DebounceHttpClient({
    required this.apiUrl,
    required this.debounceDuration,
    required this.timeoutDuration,
    Map<String, String>? defaultHeaders,
    http.Client? client,
  })  : defaultHeaders =
            defaultHeaders ?? const {'Content-Type': 'application/json'},
        _client = client ?? http.Client() {
    _initConnectivity();
    _connectivitySubscription = Connectivity().onConnectivityChanged.listen(
      (results) {
        // results is a list of ConnectivityResult
        final firstResult = results.isNotEmpty ? results.first : ConnectivityResult.none;
        _updateConnectionStatus(firstResult);
      },
    );
  }

  Future<void> _initConnectivity() async {
    final result = await Connectivity().checkConnectivity();
    _updateConnectionStatus(result.first);
  }

  void _updateConnectionStatus(ConnectivityResult result) {
    _isConnected = result != ConnectivityResult.none;
  }

  /// Make a GET request with debounce and network awareness
  Future<DebounceHttpResult<http.Response>> get(String path, {Map<String, String>? headers}) async {
    if (!_isConnected) {
      return DebounceHttpResult(error: 'No internet connection', data: {} as http.Response);
    }
    return _debouncedRequest(() => _client.get(Uri.parse("$apiUrl/$path"),
        headers: {...defaultHeaders, if (headers != null) ...headers}));
  }

  /// Make a POST request with debounce and network awareness
  Future<DebounceHttpResult<http.Response>> post(Uri url,
      {Map<String, String>? headers, Object? body, Encoding? encoding}) async {
    if (!_isConnected) {
      // ignore: cast_from_null_always_fails
      return DebounceHttpResult(error: 'No internet connection', data: {} as http.Response);
    }
    return _debouncedRequest(() => _client.post(url,
        headers: {...defaultHeaders, if (headers != null) ...headers},
        body: body,
        encoding: encoding));
  }

  Future<DebounceHttpResult<http.Response>> _debouncedRequest(
      Future<http.Response> Function() requestFunction) {
    _debounceTimer?.cancel();

    final completer = Completer<DebounceHttpResult<http.Response>>();

    _debounceTimer = Timer(debounceDuration, () async {
      if (!_isConnected) {
        completer.complete(DebounceHttpResult(error: 'No internet connection during request', data: {} as http.Response));
        return;
      }

      try {
        final response = await requestFunction().timeout(timeoutDuration);
       return completer.complete(DebounceHttpResult(data: response));
      } on TimeoutException {
      return  completer.complete(DebounceHttpResult(error: 'Operation timed out', data: {} as http.Response));
      } catch (e) {
      return  completer.complete(DebounceHttpResult(error: e.toString(), data: {} as http.Response));
      }
    });

    return completer.future;
  }

  void dispose() {
    _debounceTimer?.cancel();
    _client.close();
    _connectivitySubscription.cancel();
  }
}

final debounceHttpClientProvider = Provider<DebounceHttpClient>((ref) {
  final client = DebounceHttpClient(
    apiUrl: ApiExtension.apiUrl,
    debounceDuration: const Duration(milliseconds: 300),
    timeoutDuration: const Duration(seconds: 3),
    );
  ref.onDispose(client.dispose);
  return client;
});


name: golbarg
description: "<PERSON><PERSON>omak"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  uuid: ^4.4.0
  http: ^1.1.0
  intl: ^0.19.0
  flutter_dotenv: ^5.1.0
  skeletonizer: ^1.1.2+1
  localstorage: ^5.0.0
  shared_preferences: ^2.2.1

  get_storage: ^2.1.1
  meta: ^1.9.1
  dart_jsonwebtoken: ^3.2.0
  flutter_svg: ^2.0.7
  flex_color_scheme: ^8.2.0
  flutter_rating_bar: ^4.0.1
  expandable: ^5.0.1
  flutter_map: ^8.1.1
  latlong2: ^0.9.1
  shamsi_date: ^1.0.2
  fluttericon: ^2.0.0
  flutter_riverpod: ^2.6.1
  riverpod_annotation: ^2.6.1
  pin_code_fields: ^8.0.1
  flutter_web_plugins:
    sdk: flutter
  url_strategy: ^0.3.0
  jwt_decoder: ^2.0.1
  permission_handler: ^11.4.0
  hooks_riverpod: ^2.6.1
  auto_route: ^10.0.1
  path_provider: ^2.1.5
  flutter_rating: ^2.0.2
  smooth_page_indicator: ^1.2.1
  flutter_spinkit: ^5.2.1
  file_picker: ^10.1.2
  persian_datetime_picker: ^3.1.0
  flutter_localizations:
    sdk: flutter

  url_launcher: ^6.3.1
  flutter_map_cancellable_tile_provider: ^3.1.0
  geolocator: ^14.0.0
  flutter_local_notifications: ^19.2.1
  mqtt_client: ^10.8.0
  flutter_launcher_icons: ^0.14.3
  flutter_app_badge: ^2.0.2
  background_fetch: ^1.3.8
  connectivity_plus: ^6.1.4

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  riverpod_generator: ^2.6.5
  build_runner: ^2.4.14
  custom_lint: ^0.7.5
  auto_route_generator: ^10.0.1
  font_awesome_flutter:
    git:
      url: https://github.com/JavidNoshadi/font_awesome_flutter.git
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  assets:
    - .env.development
    - images/
  fonts:
    - family: Dana
      fonts:
        - asset: fonts/dana.ttf
          style: italic
    - family: Yekan
      fonts:
        - asset: fonts/yekan.ttf
          style: italic
    - family: iranSans
      fonts:
        - asset: fonts/iranSans.ttf
          style: italic      
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
analyzer:
  plugins:
    - custom_lint

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "images/logo.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "images/logo.png"
    background_color: "#d3a92f"
    theme_color: "#d3a92f"
  windows:
    generate: true
    image_path: "images/logo.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "images/logo.png"
{"name": "server", "version": "1.0.0", "main": "index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon --exec ts-node ./src/app.ts"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/client-s3": "^3.826.0", "bcrypt": "^6.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-fileupload": "^1.5.1", "express-rate-limit": "^7.5.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "nodemon": "^3.1.10", "sharp": "^0.34.2", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.9", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.0.4", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}
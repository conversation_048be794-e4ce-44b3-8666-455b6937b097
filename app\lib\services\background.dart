import 'package:background_fetch/background_fetch.dart';

import 'package:golbarg/services/notification.dart';

/// Headless task function to run when app is terminated
@pragma('vm:entry-point')
Future<void> backgroundFetchHeadlessTask(HeadlessTask task) async {
  final String taskId = task.taskId;
  final bool isTimeout = task.timeout;
  final notificationService = NotificationService();
  try {
    if (isTimeout) {
      print("[BackgroundFetch][Headless] Task timed out: $taskId");

      notificationService.showNotification("timeout", "body", "link");
      return;
    }

    print("[BackgroundFetch][Headless] Event received: $taskId");
    notificationService.showNotification("title", "body", "link");
    return;
  } catch (e, stackTrace) {
    print("[BackgroundFetch][Headless][Error] $e");
    notificationService.showNotification("error", "body", "link");
    print(stackTrace);
  } finally {
    print("[BackgroundFetch][Headless] Task finished: $taskId");
    notificationService.showNotification("finally", "body", "link");
    BackgroundFetch.finish(taskId);
  }
}

/// Initialize BackgroundFetch with desired configuration
Future<void> initBackgroundFetch() async {
  try {
    final config = BackgroundFetchConfig(
      minimumFetchInterval: 15, // in minutes
      stopOnTerminate: false,
      enableHeadless: true,
      requiresBatteryNotLow: false,
      requiresCharging: false,
      requiresStorageNotLow: false,
      requiresDeviceIdle: false,
      startOnBoot: true,
      // You can specify the network type if needed
      requiredNetworkType: NetworkType.NONE,
    );
    await BackgroundFetch.configure(
        config, fetchEventHandler, timeoutEventHandler);
    await BackgroundFetch.start();
    print("[BackgroundFetch] Successfully configured");
  } catch (e, stackTrace) {
    print("[BackgroundFetch][Error] Failed to initialize: $e");
    print(stackTrace);
  }
}

/// Handles background fetch events
Future<void> fetchEventHandler(String taskId) async {
  try {
    print("[BackgroundFetch] Event received: $taskId");
  } catch (e, stackTrace) {
    print("[BackgroundFetch][Error] During fetch event: $e");
    print(stackTrace);
  } finally {
    BackgroundFetch.finish(taskId);
  }
}

/// Handles timeout scenarios for background fetch tasks
Future<void> timeoutEventHandler(String taskId) async {
  try {
    print("[BackgroundFetch] Timeout for task: $taskId");
  } catch (e, stackTrace) {
    print("[BackgroundFetch][Error] During timeout handler: $e");
    print(stackTrace);
  } finally {
    BackgroundFetch.finish(taskId);
  }
}

/// fetch for [UNREAD] notification from server

import express, { Request, Response } from 'express';
import connectDB from './config/database';
import dotenv from 'dotenv';
import index from "./routes/v1/index"
import auth from "./routes/v1/auth";
import cookieParser from 'cookie-parser';

dotenv.config();
connectDB()

const app = express();
const port = process.env.PORT || 5000;
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static('public'));
app.use(cookieParser());
app.use('/api/v1', index);
app.use('/api/v1/auth', auth);

app.listen(port, () => {
  console.log(`Server is running at http://localhost:${port}`);
});
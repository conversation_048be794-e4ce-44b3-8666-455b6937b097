{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "app",
            "cwd": "app",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "app (profile mode)",
            "cwd": "app",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "app (release mode)",
            "cwd": "app",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "Launch",
            "type": "dart",
            "deviceId": "chrome",
            "args": ["--web-port=4000", "--web-hostname=localhost"],
            "cwd": "app",
            "request": "launch",
            "program": "lib/main.dart"
        }
    ]
}
import 'dart:async';
import 'dart:math';
import 'dart:typed_data';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:http/http.dart' as http;

// ------------------- Notification Service -------------------

class NotificationService {
  final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();
  late BuildContext context;

  final String groupKey = 'ir.gol.app.notifications';

  final StreamController<String?> selectNotificationStream =
      StreamController<String?>.broadcast();

  Future<void> init() async {
    // Initialize plugin
    const androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    final initSettings = InitializationSettings(android: androidSettings);
    await _notificationsPlugin.initialize(
      initSettings,
      onDidReceiveNotificationResponse: (response) {
        print(response.actionId);
        // AutoRouter.of(context).navigate(Uri.parse(response.actionId) as PageRouteInfo);
      },
    );
  }

  Future<void> showNotification(String title, String body, String link) async {
    final Random _rand = Random();
    int generateNotificationId() => _rand.nextInt(2147483647);
    final int id = generateNotificationId();

    final androidDetails = AndroidNotificationDetails(
        'channel_id', 'channel_name',
        channelDescription: 'channel_description',
        importance: Importance.max,
        priority: Priority.defaultPriority,
        icon: '@mipmap/ic_launcher',
        styleInformation: BigTextStyleInformation(body),
        actions: [
          AndroidNotificationAction(link, "navigate"),
        ]);

    final platformDetails = NotificationDetails(android: androidDetails);

    await _notificationsPlugin.show(id, title, body, platformDetails,
        payload: link);
  }

  Future<void> showNotificationWithImage(
      String title, String body, String imageUrl) async {
    final imageBytes = await downloadImage(imageUrl);
    if (imageBytes.isEmpty) {
      await showNotification(title, body, '');
      return;
    }

    final styleInfo = BigPictureStyleInformation(
      ByteArrayAndroidBitmap(imageBytes),
      contentTitle: title,
      summaryText: body,
    );

    final androidDetails = AndroidNotificationDetails(
      'channel_id',
      'channel_name',
      channelDescription: 'channel_description',
      importance: Importance.max,
      priority: Priority.high,
      styleInformation: styleInfo,
      icon: '@mipmap/ic_launcher',
      groupKey: groupKey,
      setAsGroupSummary: false,
    );

    final platformDetails = NotificationDetails(android: androidDetails);
    final Random _rand = Random();
    int generateNotificationId() => _rand.nextInt(2147483647);
    final int id = generateNotificationId();
    await _notificationsPlugin.show(id, title, body, platformDetails);
    await _showGroupSummary();
  }

  Future<void> _showGroupSummary() async {
    final androidDetails = AndroidNotificationDetails(
      'channel_id',
      'channel_name',
      channelDescription: 'channel_description',
      importance: Importance.max,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
      styleInformation: const InboxStyleInformation([]),
      groupKey: groupKey,
      setAsGroupSummary: true,
    );

    final platformDetails = NotificationDetails(android: androidDetails);
    await _notificationsPlugin.show(
        0, 'Notifications', 'You have new messages', platformDetails);
  }

  Future<Uint8List> downloadImage(String url) async {
    try {
      final response = await http.get(Uri.parse(url));
      return response.bodyBytes;
    } catch (e) {
      print('Image download error: $e');
      return Uint8List(0);
    }
  }
}

// ------------------- Provider -------------------

final notificationServiceProvider = Provider<NotificationService>((ref) {
  final service = NotificationService();
  service.init();
  return service;
});

import mongoose from "mongoose";

const mobileSchema = new mongoose.Schema({
    mobile: {
        type: String,
        required: true,
        unique: true,
    },
    user: {
        type: mongoose.SchemaTypes.ObjectId,
        ref: "User",
        required: true,
    },
    isActivated:{
        type: Boolean,
        required: true,
        default: false,
    },
    activation_uuid:{
        type: String
    },
    OPT:{
        type: Number
    },
    OPT_exp:{
        type: Date
    },
})


const Mobile = mongoose.model("Mobile", mobileSchema)

export default Mobile;
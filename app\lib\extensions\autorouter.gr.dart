// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_route/auto_route.dart' as _i8;
import 'package:golbarg/app.dart' as _i2;
import 'package:golbarg/screens/auth/Login.dart' as _i4;
import 'package:golbarg/screens/others/404.dart' as _i5;
import 'package:golbarg/screens/others/FAQ.dart' as _i1;
import 'package:golbarg/screens/others/Privacy.dart' as _i6;
import 'package:golbarg/screens/others/Welcome.dart' as _i7;
import 'package:golbarg/screens/public/Home.dart' as _i3;

/// generated route for
/// [_i1.FAQScreen]
class FAQRoute extends _i8.PageRouteInfo<void> {
  const FAQRoute({List<_i8.PageRouteInfo>? children})
      : super(FAQRoute.name, initialChildren: children);

  static const String name = 'FAQRoute';

  static _i8.PageInfo page = _i8.PageInfo(
    name,
    builder: (data) {
      return const _i1.FAQScreen();
    },
  );
}

/// generated route for
/// [_i2.Gol]
class Gol extends _i8.PageRouteInfo<void> {
  const Gol({List<_i8.PageRouteInfo>? children})
      : super(Gol.name, initialChildren: children);

  static const String name = 'Gol';

  static _i8.PageInfo page = _i8.PageInfo(
    name,
    builder: (data) {
      return const _i2.Gol();
    },
  );
}

/// generated route for
/// [_i3.HomeScreen]
class HomeRoute extends _i8.PageRouteInfo<void> {
  const HomeRoute({List<_i8.PageRouteInfo>? children})
      : super(HomeRoute.name, initialChildren: children);

  static const String name = 'HomeRoute';

  static _i8.PageInfo page = _i8.PageInfo(
    name,
    builder: (data) {
      return const _i3.HomeScreen();
    },
  );
}

/// generated route for
/// [_i4.LoginScreen]
class LoginRoute extends _i8.PageRouteInfo<void> {
  const LoginRoute({List<_i8.PageRouteInfo>? children})
      : super(LoginRoute.name, initialChildren: children);

  static const String name = 'LoginRoute';

  static _i8.PageInfo page = _i8.PageInfo(
    name,
    builder: (data) {
      return const _i4.LoginScreen();
    },
  );
}

/// generated route for
/// [_i5.NotFound]
class NotFound extends _i8.PageRouteInfo<void> {
  const NotFound({List<_i8.PageRouteInfo>? children})
      : super(NotFound.name, initialChildren: children);

  static const String name = 'NotFound';

  static _i8.PageInfo page = _i8.PageInfo(
    name,
    builder: (data) {
      return _i5.NotFound();
    },
  );
}

/// generated route for
/// [_i6.PrivacyScreen]
class PrivacyRoute extends _i8.PageRouteInfo<void> {
  const PrivacyRoute({List<_i8.PageRouteInfo>? children})
      : super(PrivacyRoute.name, initialChildren: children);

  static const String name = 'PrivacyRoute';

  static _i8.PageInfo page = _i8.PageInfo(
    name,
    builder: (data) {
      return const _i6.PrivacyScreen();
    },
  );
}

/// generated route for
/// [_i7.WelcomeScreen]
class WelcomeRoute extends _i8.PageRouteInfo<void> {
  const WelcomeRoute({List<_i8.PageRouteInfo>? children})
      : super(WelcomeRoute.name, initialChildren: children);

  static const String name = 'WelcomeRoute';

  static _i8.PageInfo page = _i8.PageInfo(
    name,
    builder: (data) {
      return const _i7.WelcomeScreen();
    },
  );
}

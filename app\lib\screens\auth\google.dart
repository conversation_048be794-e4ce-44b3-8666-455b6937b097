/* import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Google OAuth Login',
      home: LoginScreen(),
    );
  }
}

class LoginScreen extends StatefulWidget {
  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  String _userEmail = '';
  bool _isLoading = false;

  // Replace these with your Google API credentials
  final String clientId = '838878631066-pv8cuiv44udgmvle3pjthdo3uvcj6q8b.apps.googleusercontent.com';
  final String clientSecret = '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';

  Future<void> _login() async {
    setState(() {
      _isLoading = true;
      _userEmail = '';
    });

    final googleClient = GoogleOAuth2Client(      
      redirectUri: 'ir.golbarg.app://oauth2redirect', 
      customUriScheme: 'ir.golbarg.app',
    );

    try {
      final tokenResponse = await googleClient.getTokenWithAuthCodeFlow(
        scopes: ['email', 'profile'],
        clientId: clientId,
        clientSecret: clientSecret
      );

      if (tokenResponse != null && tokenResponse.accessToken != null) {
        // Fetch user info using the access token
        final email = await _fetchUserEmail(tokenResponse.accessToken!);
        setState(() {
          _userEmail = email ?? 'Unable to fetch email';
        });
      }
    } catch (e) {
      print('Login error: $e');
      setState(() {
        _userEmail = 'Login failed';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<String?> _fetchUserEmail(String accessToken) async {
    final response = await Uri.parse(
        'https://www.googleapis.com/oauth2/v1/userinfo?alt=json');

    final headers = {
      'Authorization': 'Bearer $accessToken',
    };

    final httpClient = HttpClient();
    try {
      final request = await httpClient.getUrl(Uri.parse(
          'https://www.googleapis.com/oauth2/v1/userinfo?alt=json'));
      request.headers.add('Authorization', 'Bearer $accessToken');

      final response = await request.close();

      if (response.statusCode == 200) {
        final responseBody = await response.transform(utf8.decoder).join();
        final userInfo = jsonDecode(responseBody);
        return userInfo['email'];
      }
    } catch (e) {
      print('Error fetching user info: $e');
    } finally {
      httpClient.close();
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Google OAuth Login'),
      ),
      body: Center(
        child: _isLoading
            ? CircularProgressIndicator()
            : Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ElevatedButton(
                    onPressed: _login,
                    child: Text('Login with Google'),
                  ),
                  SizedBox(height: 20),
                  Text(
                    _userEmail.isNotEmpty
                        ? 'Logged in as: $_userEmail'
                        : 'Not logged in',
                  ),
                ],
              ),
      ),
    );
  }
} */
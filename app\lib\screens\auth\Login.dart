import 'package:auto_route/annotations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:golbarg/screens/auth/google.dart';
import 'package:golbarg/services/googleSignIn.dart';

@RoutePage()
class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});
  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

final formController = StateProvider((ref) => {});

class _LoginScreenState extends ConsumerState<LoginScreen> {

  bool IsLoginSeq = true;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const Text('ورود'),
        ),
        body: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
         // Image.asset("./images/logo.png"),
            IsLoginSeq ? Text("Login") : Text("Signup"),
            buildInput(
              hintText: 'ایمیل',
              labelText: 'ایمیل',
              obscureText: false,
              controller: TextEditingController(),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'لطفا ایمیل خود را وارد کنید';
                }
                if (!value.contains('@')) {
                  return 'لطفا ایمیل خود را وارد کنید';
                }
                return null;
              },
              icon: Icon(FontAwesomeIcons.user),
            ),
            buildInput(
              hintText: 'Password',
              labelText: 'Password',
              obscureText: true,
              controller: TextEditingController(),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your password';
                }
                return null;
              },
              icon: Icon(FontAwesomeIcons.lock),
            ),
            ElevatedButton(
              style: ButtonStyle(
                minimumSize: WidgetStateProperty.all(Size(100, 50)),
              ),
              onPressed: () {
                
              },
              child: Text('ورود'),
            ),
            SignInDemo(),
            SizedBox(
              height: 200,
            ),
            Row(
              children: [
                TextButton(
                    onPressed: () {
                      setState(() {
                        IsLoginSeq = !IsLoginSeq;
                      });
                    },
                    child: Text("signup"))
              ],
            )
          ],
        ));
  }
Widget buildInput({
  required String hintText,
  required String labelText,
  required bool obscureText,
  required controller,
  required String? Function(String?)? validator,
  required Widget icon,
}) {
  return Padding(
    padding: const EdgeInsets.all(8.0),
    child: TextFormField(
      controller: controller,
      obscureText: obscureText,
      decoration: InputDecoration(
        hintText: hintText,
        labelText: labelText,
        prefixIcon: icon,
      ),
      validator: validator,
    ),
  );
}
}


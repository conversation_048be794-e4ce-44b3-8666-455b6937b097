/* // Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// ignore_for_file: avoid_print

import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:http/http.dart' as http;
import './googleSignIn/web_wrapper.dart' as web;
import 'package:google_sign_in_platform_interface/src/types.dart';

/// Replace with your actual client IDs.
const String clientId =
  '************-pv8cuiv44udgmvle3pjthdo3uvcj6q8b.apps.googleusercontent.com';
const String serverClientId =
  '************-vujaaj27nqrv0i70o1modlddd1oqkt6a.apps.googleusercontent.com';

/// The OAuth scopes your app needs.
const List<String> scopes = [
  'https://www.googleapis.com/auth/userinfo.email',
];

class SignInDemo extends StatefulWidget {
  const SignInDemo({Key? key}) : super(key: key);

  @override
  State<SignInDemo> createState() => _SignInDemoState();
}

class _SignInDemoState extends State<SignInDemo> {
  GoogleSignInAccount? _currentUser;
  bool _isAuthorized = false;
  String _contactInfo = '';
  String _errorMessage = '';
  String _serverAuthCode = '';

  @override
  void initState() {
  super.initState();
  final signIn = GoogleSignIn.instance;
  unawaited(
    signIn
      .initialize(clientId: clientId, serverClientId: serverClientId)
      .then((_) {
    signIn.authenticationEvents
      .listen(_handleAuthenticationEvent)
      .onError(_handleAuthenticationError);
    signIn.attemptLightweightAuthentication();
    }),
  );
  }

  Future<void> _handleAuthenticationEvent(
    GoogleSignInAuthenticationEvent event) async {
  final user =
    event is GoogleSignInAuthenticationEventSignIn ? event.user : null;
  final authorization =
    await user?.authorizationClient.authorizationForScopes(scopes);

  setState(() {
    _currentUser = user;
    _isAuthorized = authorization != null;
    _errorMessage = '';
  });

  if (user != null && authorization != null) {
    unawaited(_fetchUserContacts(user));
  }
  }

  Future<void> _handleAuthenticationError(Object e) async {
  setState(() {
    _currentUser = null;
    _isAuthorized = false;
    _errorMessage = e is GoogleSignInException
      ? _errorMessageFromSignInException(e)
      : 'Unknown error: $e';
  });
  }

  Future<void> _fetchUserContacts(GoogleSignInAccount user) async {
  setState(() {
    _contactInfo = 'Loading contact info...';
  });

  final headers = await user.authorizationClient.authorizationHeaders(scopes);
  if (headers == null) {
    setState(() {
    _contactInfo = '';
    _errorMessage = 'Failed to construct authorization headers.';
    });
    return;
  }

  final response = await http.get(
    Uri.parse(
      'https://people.googleapis.com/v1/people/me/connections?requestMask.includeField=person.names'),
    headers: headers,
  );

  if (response.statusCode != 200) {
    if (response.statusCode == 401 || response.statusCode == 403) {
    setState(() {
      _isAuthorized = false;
      _errorMessage =
        'People API responded with ${response.statusCode}. Please re-authorize.';
    });
    } else {
    print('People API response: ${response.body}');
    setState(() {
      _contactInfo =
        'People API error: ${response.statusCode}. Check logs.';
    });
    }
    return;
  }

  final data = json.decode(response.body) as Map<String, dynamic>;
  final contactName = _pickFirstNamedContact(data);
  setState(() {
    _contactInfo = contactName != null
      ? 'I see you know $contactName!'
      : 'No contacts to display.';
  });
  }

  String? _pickFirstNamedContact(Map<String, dynamic> data) {
  final connections = data['connections'] as List<dynamic>? ?? [];
  for (final c in connections) {
    final contact = c as Map<String, dynamic>;
    final names = contact['names'] as List<dynamic>?;
    if (names != null && names.isNotEmpty) {
    final name = names.firstWhere(
      (n) => (n as Map).containsKey('displayName'),
      orElse: () => null,
    ) as Map<String, dynamic>?;
    if (name != null && name['displayName'] != null) {
      return name['displayName'] as String;
    }
    }
  }
  return null;
  }

  Future<void> _requestScopes(GoogleSignInAccount user) async {
  try {
    final authorization =
      await user.authorizationClient.authorizeScopes(scopes);
    setState(() {
    _isAuthorized = authorization != null;
    _errorMessage = '';
    });
    await _fetchUserContacts(user);
  } on GoogleSignInException catch (e) {
    setState(() {
    _errorMessage = _errorMessageFromSignInException(e);
    });
  }
  }

  Future<void> _requestServerAuthCode(GoogleSignInAccount user) async {
  try {
    final serverAuth = await user.authorizationClient.authorizeServer(scopes);
    setState(() {
    _serverAuthCode = serverAuth?.serverAuthCode ?? '';
    });
  } on GoogleSignInException catch (e) {
    setState(() {
    _errorMessage = _errorMessageFromSignInException(e);
    });
  }
  }

  Future<void> _signOut() async {
  await GoogleSignIn.instance.disconnect();
  }

  Widget _buildBody() {
  return Column(
    mainAxisAlignment: MainAxisAlignment.spaceAround,
    children: [
    if (_currentUser != null)
      ..._buildAuthenticatedSection(_currentUser!)
    else
      ..._buildUnauthenticatedSection(),
    if (_errorMessage.isNotEmpty)
      Text(_errorMessage, style: const TextStyle(color: Colors.red)),
    ],
  );
  }

  List<Widget> _buildAuthenticatedSection(GoogleSignInAccount user) {
  return [
    ListTile(
    leading: GoogleUserCircleAvatar(identity: user),
    title: Text(user.displayName ?? ''),
    subtitle: Text(user.email),
    ),
    const Text('Signed in successfully.'),
    if (_isAuthorized) ...[
    if (_contactInfo.isNotEmpty) Text(_contactInfo),
    ElevatedButton(
      onPressed: () => _fetchUserContacts(user),
      child: const Text('Refresh Contacts'),
    ),
    if (_serverAuthCode.isEmpty)
      ElevatedButton(
      onPressed: () => _requestServerAuthCode(user),
      child: const Text('Get Server Auth Code'),
      )
    else
      Text('Server auth code:\n$_serverAuthCode'),
    ] else ...[
    const Text('Authorization needed to access contacts.'),
    ElevatedButton(
      onPressed: () => _requestScopes(user),
      child: const Text('Request Permissions'),
    ),
    ],
    ElevatedButton(
    onPressed: _signOut,
    child: const Text('Sign Out'),
    ),
  ];
  }

  List<Widget> _buildUnauthenticatedSection() {
  return [
    const Text('You are not signed in.'),
    if (GoogleSignIn.instance.supportsAuthenticate())
    ElevatedButton(
      onPressed: () async {
      try {
        await GoogleSignIn.instance.authenticate();
      } catch (e) {
        setState(() {
        _errorMessage = e.toString();
        });
      }
      },
      child: const Text('Sign In'),
    )
    else if (kIsWeb)
    web.renderButton()
    else
    const Text('Sign-in not supported on this platform.'),
  ];
  }

  @override
  Widget build(BuildContext context) => _buildBody();

  String _errorMessageFromSignInException(GoogleSignInException e) {
  switch (e.code) {
    case GoogleSignInExceptionCode.canceled:
    return 'Sign in canceled';
    default:
    return 'Error: ${e.code}: ${e.description}';
  }
  }
}
 */
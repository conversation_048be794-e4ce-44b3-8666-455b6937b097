buildscript {
    repositories {
        google()
        mavenCentral()

    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.7.3' // Use a valid version
      //  coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.3'

    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}


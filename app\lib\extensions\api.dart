import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Export the extension
export 'api.dart';

extension ApiExtension on Ref {
  /// Returns the API URL based on the platform.
 static String get apiUrl {
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
      case TargetPlatform.iOS:
        return dotenv.env['api_mobile'] ?? '';
      case TargetPlatform.linux:
      case TargetPlatform.macOS:
      case TargetPlatform.windows:
        return dotenv.env['api_desktop'] ?? '';
      case TargetPlatform.fuchsia:
        return dotenv.env['api_fuchsia'] ?? '';
      }
  }
}

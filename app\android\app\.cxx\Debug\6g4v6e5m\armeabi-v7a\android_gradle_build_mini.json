{"buildFiles": ["C:\\Users\\<USER>\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\gol\\gol\\app\\android\\app\\.cxx\\Debug\\6g4v6e5m\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\gol\\gol\\app\\android\\app\\.cxx\\Debug\\6g4v6e5m\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}